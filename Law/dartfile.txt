Icon(Icons.cloud_upload, size: 30),
                  Text('Choose file to upload'),
                ],
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Document Name',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                final newDoc = Document(
                  documentId: 'doc_${DateTime.now().millisecondsSinceEpoch}',
                  name: nameController.text,
                  url: 'dummy_url',
                  uploadedAt: DateTime.now(),
                );
                currentCase.documents.add(newDoc);
                setState(() {});
              }
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Document uploaded')),
              );
            },
            child: Text('Upload'),
          ),
        ],
      ),
    );
  }

  void _showAddResearchDialog() {
    final titleController = TextEditingController();
    final summaryController = TextEditingController();
    final sourceController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Research Finding'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: 'Research Title',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              TextField(
                controller: summaryController,
                decoration: InputDecoration(
                  labelText: 'Summary',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              SizedBox(height: 16),
              TextField(
                controller: sourceController,
                decoration: InputDecoration(
                  labelText: 'Source URL',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Research finding added')),
              );
            },
            child: Text('Add Research'),
          ),
        ],
      ),
    );
  }

  void _generateAISummary() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generating AI Summary...'),
          ],
        ),
      ),
    );

    Future.delayed(Duration(seconds: 3), () {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('AI Summary generated successfully')),
      );
    });
  }

  void _shareAISummary() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('AI Summary shared successfully')),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

// Add Hearing Dialog
class AddHearingDialog extends StatefulWidget {
  final Case case;

  AddHearingDialog({required this.case});

  @override
  _AddHearingDialogState createState() => _AddHearingDialogState();
}

class _AddHearingDialogState extends State<AddHearingDialog> {
  final _courtController = TextEditingController();
  final _judgeController = TextEditingController();
  final _timeController = TextEditingController();
  DateTime _selectedDate = DateTime.now().add(Duration(days: 7));

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Add Hearing'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Hearing Date',
                  border: OutlineInputBorder(),
                ),
                child: Text(DateFormat('MMM dd, yyyy').format(_selectedDate)),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _timeController,
              decoration: InputDecoration(
                labelText: 'Time (e.g., 10:00 AM)',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _courtController,
              decoration: InputDecoration(
                labelText: 'Court',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _judgeController,
              decoration: InputDecoration(
                labelText: 'Judge',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_timeController.text.isNotEmpty &&
                _courtController.text.isNotEmpty &&
                _judgeController.text.isNotEmpty) {
              final newHearing = Hearing(
                hearingId: 'hearing_${DateTime.now().millisecondsSinceEpoch}',
                date: _selectedDate,
                time: _timeController.text,
                court: _courtController.text,
                judge: _judgeController.text,
                status: 'Scheduled',
              );
              widget.case.hearings.add(newHearing);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Hearing added successfully')),
              );
            }
          },
          child: Text('Add Hearing'),
        ),
      ],
    );
  }

  void _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  void dispose() {
    _courtController.dispose();
    _judgeController.dispose();
    _timeController.dispose();
    super.dispose();
  }
}

// Diary Screen
class DiaryScreen extends StatefulWidget {
  @override
  _DiaryScreenState createState() => _DiaryScreenState();
}

class _DiaryScreenState extends State<DiaryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Diary'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'Today'),
            Tab(text: 'This Week'),
            Tab(text: 'All Events'),
            Tab(text: 'Calendar'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => _showAddEventDialog(),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayTab(),
          _buildThisWeekTab(),
          _buildAllEventsTab(),
          _buildCalendarTab(),
        ],
      ),
    );
  }

  Widget _buildTodayTab() {
    final todayEvents = AppState.dummyDiaryEntries
        .where((entry) => _isSameDay(entry.date, DateTime.now()))
        .toList();

    return _buildEventsList(todayEvents, 'No events scheduled for today');
  }

  Widget _buildThisWeekTab() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(Duration(days: 6));

    final weekEvents = AppState.dummyDiaryEntries
        .where((entry) => entry.date.isAfter(startOfWeek.subtract(Duration(days: 1))) &&
                          entry.date.isBefore(endOfWeek.add(Duration(days: 1))))
        .toList();

    return _buildEventsList(weekEvents, 'No events scheduled for this week');
  }

  Widget _buildAllEventsTab() {
    return _buildEventsList(AppState.dummyDiaryEntries, 'No events found');
  }

  Widget _buildCalendarTab() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  Text('Calendar View', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text('Calendar Widget\n(Calendar integration would go here)'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Expanded(
            child: _buildEventsList(
              AppState.dummyDiaryEntries.take(3).toList(),
              'No upcoming events'
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsList(List<DiaryEntry> events, String emptyMessage) {
    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_today, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(emptyMessage, style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return Card(
          margin: EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getEventColor(event.type),
              child: Icon(_getEventIcon(event.type), color: Colors.white),
            ),
            title: Text(event.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(event.description),
                SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey),
                    SizedBox(width: 4),
                    Text(
                      '${DateFormat('MMM dd, yyyy').format(event.date)}${event.time != null ? ' at ${event.time}' : ''}',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                if (event.location != null) ...[
                  SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(Icons.location_on, size: 16, color: Colors.grey),
                      SizedBox(width: 4),
                      Text(event.location!, style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ],
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  child: Text('Edit'),
                  value: 'edit',
                ),
                PopupMenuItem(
                  child: Text('Delete'),
                  value: 'delete',
                ),
              ],
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditEventDialog(event);
                } else if (value == 'delete') {
                  _showDeleteEventDialog(event);
                }
              },
            ),
          ),
        );
      },
    );
  }

  Color _getEventColor(String type) {
    switch (type) {
      case 'Hearing':
        return Colors.red;
      case 'Meeting':
        return Colors.blue;
      case 'Deadline':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getEventIcon(String type) {
    switch (type) {
      case 'Hearing':
        return Icons.gavel;
      case 'Meeting':
        return Icons.people;
      case 'Deadline':
        return Icons.schedule;
      default:
        return Icons.event;
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  void _showAddEventDialog() {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final locationController = TextEditingController();
    final timeController = TextEditingController();
    String selectedType = 'Meeting';
    DateTime selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Add Event'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: InputDecoration(
                    labelText: 'Event Type',
                    border: OutlineInputBorder(),
                  ),
                  items: ['Meeting', 'Hearing', 'Deadline', 'Other']
                      .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedType = value!;
                    });
                  },
                ),
                SizedBox(height: 16),
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: 'Title',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(Duration(days: 365)),
                    );
                    if (picked != null) {
                      setState(() {
                        selectedDate = picked;
                      });
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'Date',
                      border: OutlineInputBorder(),
                    ),
                    child: Text(DateFormat('MMM dd, yyyy').format(selectedDate)),
                  ),
                ),
                SizedBox(height: 16),
                TextField(
                  controller: timeController,
                  decoration: InputDecoration(
                    labelText: 'Time (optional)',
                    border: OutlineInputBorder(),
                    hintText: 'e.g., 10:00 AM',
                  ),
                ),
                SizedBox(height: 16),
                TextField(
                  controller: locationController,
                  decoration: InputDecoration(
                    labelText: 'Location (optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty && descriptionController.text.isNotEmpty) {
                  final newEvent = DiaryEntry(
                    entryId: 'diary_${DateTime.now().millisecondsSinceEpoch}',
                    type: selectedType,
                    title: titleController.text,
                    description: descriptionController.text,
                    date: selectedDate,
                    time: timeController.text.isNotEmpty ? timeController.text : null,
                    location: locationController.text.isNotEmpty ? locationController.text : null,
                  );
                  AppState.dummyDiaryEntries.add(newEvent);
                  Navigator.pop(context);
                  setState(() {});
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Event added successfully')),
                  );
                }
              },
              child: Text('Add Event'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditEventDialog(DiaryEntry event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit functionality for ${event.title}')),
    );
  }

  void _showDeleteEventDialog(DiaryEntry event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              AppState.dummyDiaryEntries.remove(event);
              Navigator.pop(context);
              setState(() {});
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Event deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

// Yellow Pages Screen
class YellowPagesScreen extends StatefulWidget {
  @override
  _YellowPagesScreenState createState() => _YellowPagesScreenState();
}

class _YellowPagesScreenState extends State<YellowPagesScreen> {
  String _selectedCategory = 'All';
  final _searchController = TextEditingController();
  List<YellowPagesEntry> _filteredEntries = AppState.dummyYellowPages;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterEntries);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Yellow Pages'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => _showAddContactDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'Search contacts...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                  ),
                ),
                SizedBox(height: 16),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: ['All', 'Advocate', 'Law Firm', 'Legal Consultant']
                        .map((category) => Padding(
                              padding: EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(category),
                                selected: _selectedCategory == category,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                    _filterEntries();
                                  });
                                },
                              ),
                            ))
                        .toList(),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _buildContactsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactsList() {
    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.contacts, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No contacts found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredEntries.length,
      itemBuilder: (context, index) {
        final entry = _filteredEntries[index];
        return Card(
          margin: EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getCategoryColor(entry.category),
              child: Text(
                entry.name.split(' ').map((word) => word[0]).take(2).join(''),
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            title: Text(entry.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(entry.category),
                SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  children: entry.specialties
                      .map((specialty) => Chip(
                            label: Text(specialty, style: TextStyle(fontSize: 10)),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  child: Row(
                    children: [
                      Icon(Icons.phone, size: 20),
                      SizedBox(width: 8),
                      Text('Call'),
                    ],
                  ),
                  value: 'call',
                ),
                PopupMenuItem(
                  child: Row(
                    children: [
                      Icon(Icons.email, size: 20),
                      SizedBox(width: 8),
                      Text('Email'),
                    ],
                  ),
                  value: 'email',
                ),
                PopupMenuItem(
                  child: Row(
                    children: [
                      Icon(Icons.info, size: 20),
                      SizedBox(width: 8),
                      Text('View Details'),
                    ],
                  ),
                  value: 'details',
                ),
              ],
              onSelected: (value) {
                switch (value) {
                  case 'call':
                    _makeCall(entry.phone);
                    break;
                  case 'email':
                    _sendEmail(entry.email);
                    break;
                  case 'details':
                    _showContactDetails(entry);
                    break;
                }
              },
            ),
          ),
        );
      },
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Advocate':
        return Colors.blue;
      case 'Law Firm':
        return Colors.green;
      case 'Legal Consultant':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _filterEntries() {
    setState(() {
      _filteredEntries = AppState.dummyYellowPages.where((entry) {
        final matchesCategory = _selectedCategory == 'All' || entry.category == _selectedCategory;
        final matchesSearch = _searchController.text.isEmpty ||
            entry.name.toLowerCase().contains(_searchController.text.toLowerCase()) ||
            entry.specialties.any((specialty) =>
                specialty.toLowerCase().contains(_searchController.text.toLowerCase()));
        return matchesCategory && matchesSearch;
      }).toList();
    });
  }

  void _showAddContactDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final emailController = TextEditingController();
    String selectedCategory = 'Advocate';
    final specialtiesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Add Contact'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: 'Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: ['Advocate', 'Law Firm', 'Legal Consultant']
                      .map((category) => DropdownMenuItem(value: category, child: Text(category)))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedCategory = value!;
                    });
                  },
                ),
                SizedBox(height: 16),
                TextField(
                  controller: phoneController,
                  decoration: InputDecoration(
                    labelText: 'Phone',
                    border: OutlineInputBorder(),
                    prefixText: '+91 ',
                  ),
                  keyboardType: TextInputType.phone,
                ),
                SizedBox(height: 16),
                TextField(
                  controller: emailController,
                  decoration: InputDecoration(
                    labelText: 'Email',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                SizedBox(height: 16),
                TextField(
                  controller: specialtiesController,
                  decoration: InputDecoration(
                    labelText: 'Specialties (comma separated)',
                    border: OutlineInputBorder(),
                    hintText: 'Civil Law, Criminal Law',
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.isNotEmpty &&
                    phoneController.text.isNotEmpty &&
                    emailController.text.isNotEmpty) {
                  final newEntry = YellowPagesEntry(
                    entryId: 'yp_${DateTime.now().millisecondsSinceEpoch}',
                    name: nameController.text,
                    category: selectedCategory,
                    phone: '+91 ${phoneController.text}',
                    email: emailController.text,
                    specialties: specialtiesController.text
                        .split(',')
                        .map((s) => s.trim())
                        .where((s) => s.isNotEmpty)
                        .toList(),
                  );
                  AppState.dummyYellowPages.add(newEntry);
                  Navigator.pop(context);
                  _filterEntries();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Contact added successfully')),
                  );
                }
              },
              child: Text('Add Contact'),
            ),
          ],
        ),
      ),
    );
  }

  void _makeCall(String phone) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calling $phone...')),
    );
  }

  void _sendEmail(String email) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening email to $email...')),
    );
  }

  void _showContactDetails(YellowPagesEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(entry.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Category: ${entry.category}'),
            SizedBox(height: 8),
            Text('Phone: ${entry.phone}'),
            SizedBox(height: 8),
            Text('Email: ${entry.email}'),
            SizedBox(height: 16),
            Text('Specialties:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: entry.specialties
                  .map((specialty) => Chip(label: Text(specialty)))
                  .toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _makeCall(entry.phone);
            },
            child: Text('Call'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

// Add Case Screen
class AddCaseScreen extends StatefulWidget {
  @override
  _AddCaseScreenState createState() => _AddCaseScreenState();
}

class _AddCaseScreenState extends State<AddCaseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  int _currentStep = 0;

  final _caseTypeController = TextEditingController();
  final _filingNumberController = TextEditingController();
  final _registrationNumberController = TextEditingController();
  final _crnController = TextEditingController();
  final _uniqueIdController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _clientEmailController = TextEditingController();
  final _clientPhoneController = TextEditingController();

  String _selectedCaseType = 'Civil';
  DateTime _filingDate = DateTime.now();
  DateTime _registrationDate = DateTime.now();
  List<String> _selectedAdvocates = [];
  List<String> _selectedInterns = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Add New Case'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                for (int i = 0; i < 3; i++)
                  Expanded(
                    child: Container(
                      height: 4,
                      margin: EdgeInsets.symmetric(horizontal: 2),
                      decoration: BoxDecoration(
                        color: i <= _currentStep ? Colors.blue : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Text('Step ${_currentStep + 1} of 3'),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildCoreDetailsStep(),
                _buildClientDetailsStep(),
                _buildAssignUsersStep(),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _pageController.previousPage(
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Text('Back'),
                    ),
                  ),
                if (_currentStep > 0) SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentStep == 2 ? _createCase : _nextStep,
                    child: Text(_currentStep == 2 ? 'Create Case' : 'Next'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade800,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoreDetailsStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Core Case Details',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              DropdownButtonFormField<String>(
                value: _selectedCaseType,
                decoration: InputDecoration(
                  labelText: 'Case Type',
                  border: OutlineInputBorder(),
                ),
                items: ['Civil', 'Criminal', 'Family', 'Corporate', 'Tax']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCaseType = value!;
                  });
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _filingNumberController,
                decoration: InputDecoration(
                  labelText: 'Filing Number',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Filing Date',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(DateFormat('MMM dd, yyyy').format(_filingDate)),
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _registrationNumberController,
                decoration: InputDecoration(
                  labelText: 'Registration Number',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Registration Date',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(DateFormat('MMM dd, yyyy').format(_registrationDate)),
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _crnController,
                decoration: InputDecoration(
                  labelText: 'CRN Number',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _uniqueIdController,
                decoration: InputDecoration(
                  labelText: 'Unique ID',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClientDetailsStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Client & Initial Documents',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            TextFormField(
              controller: _clientNameController,
              decoration: InputDecoration(
                labelText: 'Client Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.isEmpty == true ? 'Required' : null,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _clientEmailController,
              decoration: InputDecoration(
                labelText: 'Client Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _clientPhoneController,
              decoration: InputDecoration(
                labelText: 'Client Phone',
                border: OutlineInputBorder(),
                prefixText: '+91 ',
              ),
              keyboardType: TextInputType.phone,
            ),
            SizedBox(height: 20),
            Text(
              'Initial Documents',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, style: BorderStyle.solid, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.cloud_upload, size: 40, color: Colors.grey),
                  SizedBox(height: 8),
                  Text('Upload Initial Documents'),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('File upload functionality')),
                      );
                    },
                    child: Text('Choose Files'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignUsersStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Assign Users',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Text(
              'Jr. Advocates',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Card(
              child: Column(
                children: [
                  CheckboxListTile(
                    title: Text('Adv. Priya Sharma'),
                    subtitle: Text('Junior Associate'),
                    value: _selectedAdvocates.contains('priya_sharma'),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedAdvocates.add('priya_sharma');
                        } else {
                          _selectedAdvocates.remove('priya_sharma');
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: Text('Adv. Rajesh Kumar'),
                    subtitle: Text('Junior Associate'),
                    value: _selectedAdvocates.contains('rajesh_kumar'),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedAdvocates.add('rajesh_kumar');
                        } else {
                          _selectedAdvocates.remove('rajesh_kumar');
                        }
                      });
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
            Text(
              'Interns',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Card(
              child: Column(
                children: [
                  CheckboxListTile(
                    title: Text('Alice Johnson'),
                    subtitle: Text('National Law School'),
                    value: _selectedInterns.contains('alice_johnson'),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedInterns.add('alice_johnson');
                        } else {
                          _selectedInterns.remove('alice_johnson');
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: Text('Rohit Gupta'),
                    subtitle: Text('Gujarat National Law University'),
                    value: _selectedInterns.contains('rohit_gupta'),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedInterns.add('rohit_gupta');
                        } else {
                          _selectedInterns.remove('rohit_gupta');
                        }
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectDate(BuildContext context, bool isFilingDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFilingDate ? _filingDate : _registrationDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFilingDate) {
          _filingDate = picked;
        } else {
          _registrationDate = picked;
        }
      });
    }
  }

  void _nextStep() {
    if (_currentStep == 0 && !_formKey.currentState!.validate()) {
      return;
    }

    _pageController.nextPage(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _createCase() {
    if (_clientNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Client name is required')),
      );
      return;
    }

    final newCase = Case(
      caseId: 'case_${DateTime.now().millisecondsSinceEpoch}',
      caseType: _selectedCaseType,
      status: 'Active',
      filingNumber: _filingNumberController.text,
      registrationNumber: _registrationNumberController.text,
      clientName: _clientNameController.text,
      createdAt: _filingDate,
      hearings: [],
      documents: [],
    );

    AppState.dummyCases.add(newCase);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Case Created!'),
        content: Text('Case ${newCase.filingNumber} has been created successfully.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _filingNumberController.dispose();
    _registrationNumberController.dispose();
    _crnController.dispose();
    _uniqueIdController.dispose();
    _clientNameController.dispose();
    _clientEmailController.dispose();
    _clientPhoneController.dispose();
    super.dispose();
  }
}

// Case Details Screen
class CaseDetailsScreen extends StatefulWidget {
  final Case? case;

  CaseDetailsScreen({this.case});

  @override
  _CaseDetailsScreenState createState() => _CaseDetailsScreenState();
}

class _CaseDetailsScreenState extends State<CaseDetailsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Case currentCase;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    currentCase = widget.case ?? AppState.dummyCases.first;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${currentCase.caseType} - ${currentCase.filingNumber}'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(text: 'Overview'),
            Tab(text: 'Hearings'),
            Tab(text: 'Documents'),
            Tab(text: 'Research'),
            Tab(text: 'AI Summary'),
            Tab(text: 'History'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildHearingsTab(),
          _buildDocumentsTab(),
          _buildResearchTab(),
          _buildAISummaryTab(),
          _buildHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Case Details', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  _buildDetailRow('Case Type', currentCase.caseType),
                  _buildDetailRow('Filing Number', currentCase.filingNumber),
                  _buildDetailRow('Registration Number', currentCase.registrationNumber),
                  _buildDetailRow('Status', currentCase.status),
                  _buildDetailRow('Client Name', currentCase.clientName),
                  _buildDetailRow('Created', DateFormat('MMM dd, yyyy').format(currentCase.createdAt)),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Assigned Users', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  ListTile(
                    leading: CircleAvatar(child: Text('JD')),
                    title: Text('John Doe'),
                    subtitle: Text('Advocate'),
                    dense: true,
                  ),
                  ListTile(
                    leading: CircleAvatar(child: Text('AJ')),
                    title: Text('Alice Johnson'),
                    subtitle: Text('Intern'),
                    dense: true,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          if (AppState.currentUser?.accessControl.canRemoveCases == true)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showUpdateStatusDialog(),
                icon: Icon(Icons.update),
                label: Text('Update Status'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildHearingsTab() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Hearings', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ElevatedButton.icon(
                onPressed: () => _showAddHearingDialog(),
                icon: Icon(Icons.add),
                label: Text('Add Hearing'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16),
            itemCount: currentCase.hearings.length,
            itemBuilder: (context, index) {
              final hearing = currentCase.hearings[index];
              return Card(
                margin: EdgeInsets.only(bottom: 12),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            DateFormat('MMM dd, yyyy').format(hearing.date),
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          Chip(
                            label: Text(hearing.status),
                            backgroundColor: Colors.blue.shade100,
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text('Time: ${hearing.time}'),
                      Text('Court: ${hearing.court}'),
                      Text('Judge: ${hearing.judge}'),
                      SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton.icon(
                            onPressed: () => _showAddNotesDialog(hearing),
                            icon: Icon(Icons.note_add),
                            label: Text('Add Notes'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentsTab() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Documents', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ElevatedButton.icon(
                onPressed: () => _showUploadDocumentDialog(),
                icon: Icon(Icons.upload_file),
                label: Text('Upload Document'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16),
            itemCount: currentCase.documents.length,
            itemBuilder: (context, index) {
              final document = currentCase.documents[index];
              return Card(
                margin: EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Icon(Icons.description),
                  title: Text(document.name),
                  subtitle: Text('Uploaded: ${DateFormat('MMM dd, yyyy').format(document.uploadedAt)}'),
                  trailing: IconButton(
                    icon: Icon(Icons.visibility),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Opening ${document.name}')),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildResearchTab() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Research Findings', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ElevatedButton.icon(
                onPressed: () => _showAddResearchDialog(),
                icon: Icon(Icons.add),
                label: Text('Add Research'),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16),
            children: [
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Relevant Property Law Precedent', style: TextStyle(fontWeight: FontWeight.bold)),
                      SizedBox(height: 8),
                      Text('Summary of key case law regarding shared property disputes...'),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.link, size: 16),
                          SizedBox(width: 4),
                          Text('Source: law.example.com/case123', style: TextStyle(color: Colors.blue)),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text('Added by: Alice Johnson • 2 days ago', style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAISummaryTab() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          if (AppState.currentUser?.accessControl.canShareAIResults == true) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _generateAISummary(),
                icon: Icon(Icons.auto_awesome),
                label: Text('Generate AI Summary'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            SizedBox(height: 16),
          ],
          Expanded(
            child: ListView(
              children: [
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Case Brief Summary', style: TextStyle(fontWeight: FontWeight.bold)),
                            if (AppState.currentUser?.accessControl.canShareAIResults == true)
                              IconButton(
                                icon: Icon(Icons.share),
                                onPressed: () => _shareAISummary(),
                              ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text('Generated on: ${DateFormat('MMM dd, yyyy').format(DateTime.now())}'),
                        SizedBox(height: 12),
                        Text(
                          'This case involves a property dispute between the client and defendant regarding shared ownership rights. Key legal precedents suggest favorable outcome based on documentation provided...',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryTab() {
    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        _buildHistoryItem(
          'Case Created',
          'Case filed and registered in system',
          DateFormat('MMM dd, yyyy').format(currentCase.createdAt),
          Icons.create,
        ),
        _buildHistoryItem(
          'Document Uploaded',
          'Initial case documents added',
          'Oct 20, 2024',
          Icons.upload_file,
        ),
        _buildHistoryItem(
          'Hearing Scheduled',
          'First hearing scheduled for Nov 02, 2024',
          'Oct 18, 2024',
          Icons.calendar_today,
        ),
        _buildHistoryItem(
          'Intern Assigned',
          'Alice Johnson assigned to case',
          'Oct 15, 2024',
          Icons.person_add,
        ),
      ],
    );
  }

  Widget _buildHistoryItem(String event, String description, String date, IconData icon) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade100,
          child: Icon(icon, color: Colors.blue),
        ),
        title: Text(event),
        subtitle: Text(description),
        trailing: Text(date, style: TextStyle(fontSize: 12)),
      ),
    );
  }

  void _showUpdateStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Case Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text('Active'),
              value: 'Active',
              groupValue: currentCase.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
            RadioListTile<String>(
              title: Text('Pending Hearing'),
              value: 'Pending Hearing',
              groupValue: currentCase.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
            RadioListTile<String>(
              title: Text('Closed'),
              value: 'Closed',
              groupValue: currentCase.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
          ],
        ),
      ),
    ).then((newStatus) {
      if (newStatus != null) {
        setState(() {
          // In real app, update the case status
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Case status updated to $newStatus')),
        );
      }
    });
  }

  void _showAddHearingDialog() {
    showDialog(
      context: context,
      builder: (context) => AddHearingDialog(case: currentCase),
    );
  }

  void _showAddNotesDialog(Hearing hearing) {
    final notesController = TextEditingController(text: hearing.notes ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Notes to Hearing'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Hearing: ${DateFormat('MMM dd, yyyy').format(hearing.date)} at ${hearing.time}'),
            SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: InputDecoration(
                labelText: 'Notes and Comments',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              hearing.notes = notesController.text;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Notes added to hearing')),
              );
            },
            child: Text('Add Notes'),
          ),
        ],
      ),
    );
  }

  void _showUploadDocumentDialog() {
    final nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upload Document'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [}

// My Cases Screen
class MyCasesScreen extends StatefulWidget {
  @override
  _MyCasesScreenState createState() => _MyCasesScreenState();
}

class _MyCasesScreenState extends State<MyCasesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Cases'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'All Cases'),
            Tab(text: 'Active'),
            Tab(text: 'Pending'),
            Tab(text: 'Closed'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () {
              showSearch(context: context, delegate: CaseSearchDelegate());
            },
          ),
          IconButton(
            icon: Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCasesList(AppState.dummyCases),
          _buildCasesList(AppState.dummyCases.where((c) => c.status == 'Active').toList()),
          _buildCasesList(AppState.dummyCases.where((c) => c.status == 'Pending Hearing').toList()),
          _buildCasesList(AppState.dummyCases.where((c) => c.status == 'Closed').toList()),
        ],
      ),
      floatingActionButton: AppState.currentUser?.accessControl.canAddCases == true
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AddCaseScreen()),
                );
              },
              child: Icon(Icons.add),
              backgroundColor: Colors.blue.shade800,
            )
          : null,
    );
  }

  Widget _buildCasesList(List<Case> cases) {
    if (cases.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No cases found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: cases.length,
      itemBuilder: (context, index) {
        final case = cases[index];
        return Card(
          margin: EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CaseDetailsScreen(case: case),
                ),
              );
            },
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          '${case.caseType} - ${case.filingNumber}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      _buildStatusChip(case.status),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    case.clientName,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                      SizedBox(width: 4),
                      Text(
                        'Filed: ${DateFormat('MMM dd, yyyy').format(case.createdAt)}',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Spacer(),
                      if (case.hearings.isNotEmpty) ...[
                        Icon(Icons.schedule, size: 16, color: Colors.orange),
                        SizedBox(width: 4),
                        Text(
                          'Next: ${DateFormat('MMM dd').format(case.hearings.first.date)}',
                          style: TextStyle(fontSize: 12, color: Colors.orange),
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (AppState.currentUser?.accessControl.canRemoveCases == true)
                        TextButton.icon(
                          onPressed: () => _showDeleteConfirmation(context, case),
                          icon: Icon(Icons.delete, size: 16),
                          label: Text('Remove'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                      TextButton.icon(
                        onPressed: () => _showAddHearingDialog(context, case),
                        icon: Icon(Icons.add, size: 16),
                        label: Text('Add Hearing'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'Active':
        color = Colors.green;
        break;
      case 'Pending Hearing':
        color = Colors.orange;
        break;
      case 'Closed':
        color = Colors.grey;
        break;
      default:
        color = Colors.blue;
    }

    return Chip(
      label: Text(
        status,
        style: TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  void _showFilterDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Filter Cases', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Text('Case Type:'),
            Wrap(
              spacing: 8,
              children: ['All', 'Civil', 'Criminal', 'Family', 'Corporate']
                  .map((type) => FilterChip(
                        label: Text(type),
                        selected: type == 'All',
                        onSelected: (selected) {},
                      ))
                  .toList(),
            ),
            SizedBox(height: 16),
            Text('Filing Date:'),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: Text('From Date'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: Text('To Date'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Reset'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Apply'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Case case) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Remove Case'),
        content: Text('Are you sure you want to remove case ${case.filingNumber}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Case ${case.filingNumber} removed')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _showAddHearingDialog(BuildContext context, Case case) {
    showDialog(
      context: context,
      builder: (context) => AddHearingDialog(case: case),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

// Case Search Delegate
class CaseSearchDelegate extends SearchDelegate {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    final results = AppState.dummyCases.where((case) =>
        case.filingNumber.toLowerCase().contains(query.toLowerCase()) ||
        case.clientName.toLowerCase().contains(query.toLowerCase()) ||
        case.caseType.toLowerCase().contains(query.toLowerCase())).toList();

    if (results.isEmpty) {
      return Center(child: Text('No cases found'));
    }

    return ListView.builder(
      itemCountimport 'package:flutter/material.dart';
import 'package:intl/intl.dart';

void main() {
  runApp(LegalCaseApp());
}

class LegalCaseApp extends StatelessWidget {
  @override
 _buildActiveJobSheetsCard(BuildContext context) {
    final assignedCount = AppState.dummyJobSheets.where((js) => js.status == 'Assigned').length;
    final inProgressCount = AppState.dummyJobSheets.where((js) => js.status == 'In Progress').length;
    final dueSoonCount = AppState.dummyJobSheets.where((js) =>
      js.dueDate.difference(DateTime.now()).inDays <= 2).length;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'My Active Job Sheets',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => Navigator.pushNamed(context, '/job-sheets'),
                  child: Text('View All'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatColumn('Assigned', assignedCount.toString(), Colors.blue),
                _buildStatColumn('In Progress', inProgressCount.toString(), Colors.orange),
                _buildStatColumn('Due Soon', dueSoonCount.toString(), Colors.red),
              ],
            ),
            SizedBox(height: 16),
            Text('Recent Job Sheets:', style: TextStyle(fontWeight: FontWeight.w500)),
            ...AppState.dummyJobSheets.take(2).map((jobSheet) => ListTile(
              title: Text(jobSheet.title),
              subtitle: Text('Due: ${DateFormat('MMM dd').format(jobSheet.dueDate)} - ${jobSheet.status}'),
              trailing: _buildStatusChip(jobSheet.status),
              dense: true,
              onTap: () => Navigator.pushNamed(context, '/job-sheets'),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatColumn(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'Assigned':
        color = Colors.blue;
        break;
      case 'In Progress':
        color = Colors.orange;
        break;
      case 'Completed':
        color = Colors.green;
        break;
      case 'Under Review':
        color = Colors.purple;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildAdvocateAccessCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Advocate Access Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.green.shade100,
                child: Icon(Icons.check, color: Colors.green),
              ),
              title: Text('Adv. Ramesh Kumar'),
              subtitle: Text('Access Granted - 3 cases assigned'),
              dense: true,
            ),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.orange.shade100,
                child: Icon(Icons.schedule, color: Colors.orange),
              ),
              title: Text('Adv. Priya Sharma'),
              subtitle: Text('Request Pending'),
              dense: true,
            ),
            SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/yellow-pages'),
              icon: Icon(Icons.add),
              label: Text('Find More Advocates'),
              style: ElevatedButton.styleFrom(
                minimumSize: Size(double.infinity, 40),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceFeatureCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voice Assistant',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showVoiceDialog(context),
                    icon: Icon(Icons.mic, size: 30),
                    label: Text('Ask about Case Status'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              'Try saying: "What\'s the cause list for today?" or "Status of case RN-2024-005"',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInternQuickActionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildQuickActionButton(
                  'My Cases',
                  Icons.folder,
                  Colors.blue,
                  () => Navigator.pushNamed(context, '/my-cases'),
                ),
                _buildQuickActionButton(
                  'Job Sheets',
                  Icons.assignment,
                  Colors.green,
                  () => Navigator.pushNamed(context, '/job-sheets'),
                ),
                _buildQuickActionButton(
                  'Diary',
                  Icons.calendar_today,
                  Colors.purple,
                  () => Navigator.pushNamed(context, '/diary'),
                ),
                _buildQuickActionButton(
                  'Yellow Pages',
                  Icons.phone_in_talk,
                  Colors.orange,
                  () => Navigator.pushNamed(context, '/yellow-pages'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String text, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          SizedBox(width: 8),
          Flexible(child: Text(text, textAlign: TextAlign.center)),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context, String userType) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.folder),
          label: 'My Cases',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.phone_in_talk),
          label: 'Yellow Pages',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.share),
          label: 'Share Research',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
      onTap: (index) {
        switch (index) {
          case 0:
            Navigator.pushNamed(context, '/my-cases');
            break;
          case 1:
            Navigator.pushNamed(context, '/yellow-pages');
            break;
          case 2:
            _showShareResearchDialog(context);
            break;
          case 3:
            _showProfile(context);
            break;
        }
      },
    );
  }

  void _showInternNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Notifications', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.assignment, color: Colors.green),
              title: Text('New Job Sheet assigned'),
              subtitle: Text('Research Property Law Precedents - Due in 4 days'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: Icon(Icons.feedback, color: Colors.blue),
              title: Text('Feedback received'),
              subtitle: Text('Good work on legal notice draft'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(
      context: context,
      delegate: GlobalSearchDelegate(),
    );
  }

  void _showProfile(BuildContext context) {
    final user = AppState.currentUser!;
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Profile', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            ListTile(
              leading: CircleAvatar(
                child: Text('${user.profile.firstName[0]}${user.profile.lastName[0]}'),
              ),
              title: Text('${user.profile.firstName} ${user.profile.lastName}'),
              subtitle: Text(user.email),
            ),
            if (user.profile.college != null)
              ListTile(
                leading: Icon(Icons.school),
                title: Text(user.profile.college!),
              ),
            if (user.profile.age != null)
              ListTile(
                leading: Icon(Icons.cake),
                title: Text('Age: ${user.profile.age}'),
              ),
            ListTile(
              leading: Icon(Icons.logout),
              title: Text('Logout'),
              onTap: () {
                AppState.currentUser = null;
                Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVoiceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Voice Assistant'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.mic, size: 60, color: Colors.blue),
            SizedBox(height: 16),
            Text('Listening...\nSpeak your query now'),
            SizedBox(height: 16),
            LinearProgressIndicator(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showVoiceResponse(context);
            },
            child: Text('Stop Listening'),
          ),
        ],
      ),
    );
  }

  void _showVoiceResponse(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Voice Response'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You asked: "What\'s the cause list for today?"'),
            SizedBox(height: 16),
            Text('Response:'),
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Today\'s hearings:\n\n1. Case RN-2024-005 at 10:00 AM\n   High Court of Karnataka\n   Justice A. Sharma\n\n2. Case CN-2024-010 at 2:00 PM\n   District Court\n   Justice B. Kumar',
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.volume_up, color: Colors.blue),
                SizedBox(width: 8),
                Text('Playing audio response...'),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Ask Another Question'),
          ),
        ],
      ),
    );
  }

  void _showShareResearchDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Share Research Findings', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Text('Select case to add research:'),
            SizedBox(height: 16),
            ...AppState.dummyCases.map((case) => ListTile(
              title: Text('${case.caseType} - ${case.filingNumber}'),
              subtitle: Text(case.clientName),
              trailing: Icon(Icons.add),
              onTap: () {
                Navigator.pop(context);
                _showAddResearchDialog(context, case);
              },
            )),
          ],
        ),
      ),
    );
  }

  void _showAddResearchDialog(BuildContext context, Case case) {
    final titleController = TextEditingController();
    final summaryController = TextEditingController();
    final sourceController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Research Finding'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  labelText: 'Research Title',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              TextField(
                controller: summaryController,
                decoration: InputDecoration(
                  labelText: 'Summary',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              SizedBox(height: 16),
              TextField(
                controller: sourceController,
                decoration: InputDecoration(
                  labelText: 'Source URL',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('File upload functionality')),
                  );
                },
                icon: Icon(Icons.attach_file),
                label: Text('Attach File'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Research finding added to ${case.filingNumber}')),
              );
            },
            child: Text('Add Research'),
          ),
        ],
      ),
    );
  }
}

// Global Search Delegate
class GlobalSearchDelegate extends SearchDelegate {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    if (query.isEmpty) {
      return Center(child: Text('Enter search term'));
    }

    final caseResults = AppState.dummyCases
        .where((case) =>
            case.filingNumber.toLowerCase().contains(query.toLowerCase()) ||
            case.clientName.toLowerCase().contains(query.toLowerCase()) ||
            case.caseType.toLowerCase().contains(query.toLowerCase()))
        .toList();

    final yellowPagesResults = AppState.dummyYellowPages
        .where((entry) =>
            entry.name.toLowerCase().contains(query.toLowerCase()) ||
            entry.category.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView(
      children: [
        if (caseResults.isNotEmpty) ...[
          Padding(
            padding: EdgeInsets.all(16),
            child: Text('Cases', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ),
          ...caseResults.map((case) => ListTile(
            title: Text('${case.caseType} - ${case.filingNumber}'),
            subtitle: Text(case.clientName),
            leading: Icon(Icons.folder),
            onTap: () {
              close(context, null);
            },
          )),
        ],
        if (yellowPagesResults.isNotEmpty) ...[
          Padding(
            padding: EdgeInsets.all(16),
            child: Text('Yellow Pages', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ),
          ...yellowPagesResults.map((entry) => ListTile(
            title: Text(entry.name),
            subtitle: Text(entry.category),
            leading: Icon(Icons.person),
            onTap: () {
              close(context, null);
            },
          )),
        ],
        if (caseResults.isEmpty && yellowPagesResults.isEmpty)
          Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text('No results found for "$query"'),
            ),
          ),
      ],
    );
  }
} build(BuildContext context) {
    return MaterialApp(
      title: 'Legal Case Management',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: WelcomeScreen(),
      routes: {
        '/login': (context) => LoginScreen(),
        '/register': (context) => PersonaSelectionScreen(),
        '/advocate-dashboard': (context) => AdvocateDashboard(),
        '/intern-dashboard': (context) => InternDashboard(),
        '/my-cases': (context) => MyCasesScreen(),
        '/case-details': (context) => CaseDetailsScreen(),
        '/diary': (context) => DiaryScreen(),
        '/yellow-pages': (context) => YellowPagesScreen(),
        '/analytics': (context) => AnalyticsScreen(),
        '/job-sheets': (context) => JobSheetsScreen(),
      },
    );
  }
}

// Intern Dashboard
class InternDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final user = AppState.currentUser!;

    return Scaffold(
      appBar: AppBar(
        title: Text('Dashboard'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          PopupMenuButton<String>(
            icon: Icon(Icons.language),
            onSelected: (String language) {
              AppState.selectedLanguage = language;
            },
            itemBuilder: (BuildContext context) {
              return AppState.languages.entries.map((entry) {
                return PopupMenuItem<String>(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList();
            },
          ),
          IconButton(
            icon: Badge(
              label: Text('2'),
              child: Icon(Icons.notifications),
            ),
            onPressed: () => _showInternNotifications(context),
          ),
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () => _showGlobalSearch(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hello, ${user.profile.firstName}!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),

            _buildInternTodaysCasesCard(context),
            SizedBox(height: 16),

            _buildActiveJobSheetsCard(context),
            SizedBox(height: 16),

            _buildAdvocateAccessCard(context),
            SizedBox(height: 16),

            _buildVoiceFeatureCard(context),
            SizedBox(height: 16),

            _buildInternQuickActionsCard(context),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(context, 'intern'),
    );
  }

  Widget _buildInternTodaysCasesCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.today, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Cases to Attend Today',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 16),
            ListTile(
              title: Text('High Court of Karnataka'),
              subtitle: Text('10:00 AM - Justice A. Sharma'),
              leading: Icon(Icons.gavel),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () => Navigator.pushNamed(context, '/case-details'),
            ),
          ],
        ),
      ),
    );
  }

  Widget

// Data Models
class User {
  final String userId;
  final String email;
  final String userType;
  final String languagePreference;
  final UserProfile profile;
  final AccessControl accessControl;

  User({
    required this.userId,
    required this.email,
    required this.userType,
    required this.languagePreference,
    required this.profile,
    required this.accessControl,
  });
}

class UserProfile {
  final String firstName;
  final String lastName;
  final String mobile;
  final String? officeName;
  final String? advocateLicenseNumber;
  final String? college;
  final int? age;

  UserProfile({
    required this.firstName,
    required this.lastName,
    required this.mobile,
    this.officeName,
    this.advocateLicenseNumber,
    this.college,
    this.age,
  });
}

class AccessControl {
  final bool canAddCases;
  final bool canRemoveCases;
  final bool canViewAnalytics;
  final bool canShareAIResults;
  final bool canGrantInternAccess;

  AccessControl({
    required this.canAddCases,
    required this.canRemoveCases,
    required this.canViewAnalytics,
    required this.canShareAIResults,
    required this.canGrantInternAccess,
  });
}

class Case {
  final String caseId;
  final String caseType;
  final String status;
  final String filingNumber;
  final String registrationNumber;
  final String clientName;
  final DateTime createdAt;
  final List<Hearing> hearings;
  final List<Document> documents;

  Case({
    required this.caseId,
    required this.caseType,
    required this.status,
    required this.filingNumber,
    required this.registrationNumber,
    required this.clientName,
    required this.createdAt,
    required this.hearings,
    required this.documents,
  });
}

class Hearing {
  final String hearingId;
  final DateTime date;
  final String time;
  final String court;
  final String judge;
  final String status;
  String? notes;

  Hearing({
    required this.hearingId,
    required this.date,
    required this.time,
    required this.court,
    required this.judge,
    required this.status,
    this.notes,
  });
}

class Document {
  final String documentId;
  final String name;
  final String url;
  final DateTime uploadedAt;

  Document({
    required this.documentId,
    required this.name,
    required this.url,
    required this.uploadedAt,
  });
}

class DiaryEntry {
  final String entryId;
  final String type;
  final String title;
  final String description;
  final DateTime date;
  final String? time;
  final String? location;

  DiaryEntry({
    required this.entryId,
    required this.type,
    required this.title,
    required this.description,
    required this.date,
    this.time,
    this.location,
  });
}

class JobSheet {
  final String jobSheetId;
  final String title;
  final String description;
  final DateTime assignedDate;
  final DateTime dueDate;
  final String status;
  final String assignedBy;

  JobSheet({
    required this.jobSheetId,
    required this.title,
    required this.description,
    required this.assignedDate,
    required this.dueDate,
    required this.status,
    required this.assignedBy,
  });
}

class YellowPagesEntry {
  final String entryId;
  final String name;
  final String category;
  final String phone;
  final String email;
  final List<String> specialties;

  YellowPagesEntry({
    required this.entryId,
    required this.name,
    required this.category,
    required this.phone,
    required this.email,
    required this.specialties,
  });
}

// Global State Management
class AppState {
  static User? currentUser;
  static String selectedLanguage = 'en';

  static final Map<String, String> languages = {
    'en': 'English',
    'hi': 'हिंदी',
    'kn': 'ಕನ್ನಡ',
    'ta': 'தமிழ்',
    'te': 'తెలుగు',
  };

  // Dummy Data
  static List<Case> dummyCases = [
    Case(
      caseId: 'case_1',
      caseType: 'Civil',
      status: 'Active',
      filingNumber: 'FN-2024-001',
      registrationNumber: 'RN-2024-005',
      clientName: 'John Doe',
      createdAt: DateTime.now().subtract(Duration(days: 30)),
      hearings: [
        Hearing(
          hearingId: 'h1',
          date: DateTime.now().add(Duration(days: 2)),
          time: '10:00 AM',
          court: 'High Court of Karnataka',
          judge: 'Justice A. Sharma',
          status: 'Scheduled',
        ),
      ],
      documents: [
        Document(
          documentId: 'd1',
          name: 'Case Filing Documents',
          url: 'dummy_url',
          uploadedAt: DateTime.now().subtract(Duration(days: 25)),
        ),
      ],
    ),
    Case(
      caseId: 'case_2',
      caseType: 'Criminal',
      status: 'Pending Hearing',
      filingNumber: 'FN-2024-002',
      registrationNumber: 'CN-2024-010',
      clientName: 'Jane Smith',
      createdAt: DateTime.now().subtract(Duration(days: 15)),
      hearings: [
        Hearing(
          hearingId: 'h2',
          date: DateTime.now().add(Duration(days: 5)),
          time: '2:00 PM',
          court: 'District Court',
          judge: 'Justice B. Kumar',
          status: 'Scheduled',
        ),
      ],
      documents: [],
    ),
    Case(
      caseId: 'case_3',
      caseType: 'Family',
      status: 'Closed',
      filingNumber: 'FN-2024-003',
      registrationNumber: 'FM-2024-015',
      clientName: 'Robert Wilson',
      createdAt: DateTime.now().subtract(Duration(days: 60)),
      hearings: [],
      documents: [
        Document(
          documentId: 'd2',
          name: 'Divorce Settlement',
          url: 'dummy_url_2',
          uploadedAt: DateTime.now().subtract(Duration(days: 45)),
        ),
      ],
    ),
  ];

  static List<DiaryEntry> dummyDiaryEntries = [
    DiaryEntry(
      entryId: 'diary_1',
      type: 'Hearing',
      title: 'Case RN-2024-005 Hearing',
      description: 'Prepare arguments for property dispute',
      date: DateTime.now().add(Duration(days: 2)),
      time: '10:00 AM',
      location: 'High Court of Karnataka',
    ),
    DiaryEntry(
      entryId: 'diary_2',
      type: 'Meeting',
      title: 'Client Meeting - Jane Smith',
      description: 'Discuss case strategy',
      date: DateTime.now().add(Duration(days: 1)),
      time: '3:00 PM',
      location: 'Office',
    ),
    DiaryEntry(
      entryId: 'diary_3',
      type: 'Deadline',
      title: 'Submit Appeal Documents',
      description: 'File appeal documents for case CN-2024-010',
      date: DateTime.now().add(Duration(days: 7)),
      time: '5:00 PM',
      location: 'Court Registry',
    ),
  ];

  static List<JobSheet> dummyJobSheets = [
    JobSheet(
      jobSheetId: 'job_1',
      title: 'Research Property Law Precedents',
      description: 'Find relevant cases for ongoing property dispute. Focus on shared ownership rights and partition laws.',
      assignedDate: DateTime.now().subtract(Duration(days: 3)),
      dueDate: DateTime.now().add(Duration(days: 4)),
      status: 'In Progress',
      assignedBy: 'Adv. Ramesh Kumar',
    ),
    JobSheet(
      jobSheetId: 'job_2',
      title: 'Draft Legal Notice',
      description: 'Prepare legal notice for client XYZ regarding contract breach',
      assignedDate: DateTime.now().subtract(Duration(days: 1)),
      dueDate: DateTime.now().add(Duration(days: 6)),
      status: 'Assigned',
      assignedBy: 'Adv. Priya Sharma',
    ),
    JobSheet(
      jobSheetId: 'job_3',
      title: 'Court Filing Preparation',
      description: 'Prepare and organize documents for court filing - Case FN-2024-001',
      assignedDate: DateTime.now().subtract(Duration(days: 5)),
      dueDate: DateTime.now().add(Duration(days: 1)),
      status: 'Under Review',
      assignedBy: 'Adv. Ramesh Kumar',
    ),
    JobSheet(
      jobSheetId: 'job_4',
      title: 'Client Interview Summary',
      description: 'Complete summary of client interview and evidence collection',
      assignedDate: DateTime.now().subtract(Duration(days: 7)),
      dueDate: DateTime.now().subtract(Duration(days: 1)),
      status: 'Completed',
      assignedBy: 'Adv. Priya Sharma',
    ),
  ];

  static List<YellowPagesEntry> dummyYellowPages = [
    YellowPagesEntry(
      entryId: 'yp_1',
      name: 'Adv. Ramesh Kumar',
      category: 'Advocate',
      phone: '+91 **********',
      email: '<EMAIL>',
      specialties: ['Criminal Law', 'Civil Law'],
    ),
    YellowPagesEntry(
      entryId: 'yp_2',
      name: 'Legal Eagles Law Firm',
      category: 'Law Firm',
      phone: '+91 9876543211',
      email: '<EMAIL>',
      specialties: ['Corporate Law', 'Tax Law'],
    ),
    YellowPagesEntry(
      entryId: 'yp_3',
      name: 'Justice Consultants',
      category: 'Legal Consultant',
      phone: '+91 9876543212',
      email: '<EMAIL>',
      specialties: ['Property Law', 'Family Law'],
    ),
    YellowPagesEntry(
      entryId: 'yp_4',
      name: 'Adv. Priya Sharma',
      category: 'Advocate',
      phone: '+91 9876543213',
      email: '<EMAIL>',
      specialties: ['Family Law', 'Women Rights'],
    ),
  ];
}

// Welcome Screen
class WelcomeScreen extends StatefulWidget {
  @override
  _WelcomeScreenState createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.blue.shade800, Colors.blue.shade400],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.gavel,
                size: 100,
                color: Colors.white,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'Contact Number',
                  border: OutlineInputBorder(),
                  prefixText: '+91 ',
                ),
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              if (widget.userType != 'Intern') ...[
                SizedBox(height: 16),
                TextFormField(
                  controller: _officeNameController,
                  decoration: InputDecoration(
                    labelText: 'Office Name',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalDetailsStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Professional Details',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            if (widget.userType != 'Intern') ...[
              TextFormField(
                controller: _licenseController,
                decoration: InputDecoration(
                  labelText: 'Advocate License Number',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'Number of Employees',
                  border: OutlineInputBorder(),
                ),
                items: ['1-5', '6-10', '10+'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (value) {},
              ),
            ] else ...[
              TextFormField(
                controller: _ageController,
                decoration: InputDecoration(
                  labelText: 'Age',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _collegeController,
                decoration: InputDecoration(
                  labelText: 'College Name',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.upload_file, size: 40, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('Upload Resume'),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('File upload functionality')),
                        );
                      },
                      child: Text('Choose File'),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReviewSubmitStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Review & Submit',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Profile Summary', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    _buildSummaryRow('Name', '${_firstNameController.text} ${_lastNameController.text}'),
                    _buildSummaryRow('Email', _emailController.text),
                    _buildSummaryRow('Phone', '+91 ${_phoneController.text}'),
                    if (widget.userType != 'Intern') ...[
                      _buildSummaryRow('Office', _officeNameController.text),
                      _buildSummaryRow('License', _licenseController.text),
                    ] else ...[
                      _buildSummaryRow('Age', _ageController.text),
                      _buildSummaryRow('College', _collegeController.text),
                    ],
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),
            CheckboxListTile(
              title: Text('I agree to Terms & Conditions'),
              value: _agreeToTerms,
              onChanged: (value) {
                setState(() {
                  _agreeToTerms = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value.isEmpty ? 'Not provided' : value)),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_currentStep == 0 && !_formKey.currentState!.validate()) {
      return;
    }

    _pageController.nextPage(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _submitForm() {
    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please agree to Terms & Conditions')),
      );
      return;
    }

    AppState.currentUser = User(
      userId: 'user_new_${DateTime.now().millisecondsSinceEpoch}',
      email: _emailController.text,
      userType: widget.userType == 'Sr. Advocate' || widget.userType == 'Jr. Advocate' ? 'Advocate' : 'Intern',
      languagePreference: AppState.selectedLanguage,
      profile: UserProfile(
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        mobile: '+91 ${_phoneController.text}',
        officeName: widget.userType != 'Intern' ? _officeNameController.text : null,
        advocateLicenseNumber: widget.userType != 'Intern' ? _licenseController.text : null,
        college: widget.userType == 'Intern' ? _collegeController.text : null,
        age: widget.userType == 'Intern' ? int.tryParse(_ageController.text) : null,
      ),
      accessControl: AccessControl(
        canAddCases: widget.userType != 'Intern',
        canRemoveCases: widget.userType != 'Intern',
        canViewAnalytics: widget.userType != 'Intern',
        canShareAIResults: widget.userType != 'Intern',
        canGrantInternAccess: widget.userType != 'Intern',
      ),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Account Created!'),
        content: Text(widget.userType == 'Intern'
          ? 'You can now request access from Advocates.'
          : 'Awaiting verification/approval.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (widget.userType == 'Intern') {
                Navigator.pushReplacementNamed(context, '/intern-dashboard');
              } else {
                Navigator.pushReplacementNamed(context, '/advocate-dashboard');
              }
            },
            child: Text('Continue'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _officeNameController.dispose();
    _licenseController.dispose();
    _collegeController.dispose();
    _ageController.dispose();
    super.dispose();
  }
}

// Advocate Dashboard
class AdvocateDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final user = AppState.currentUser!;

    return Scaffold(
      appBar: AppBar(
        title: Text('Dashboard'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          _buildLanguageSelector(context),
          IconButton(
            icon: Badge(
              label: Text('3'),
              child: Icon(Icons.notifications),
            ),
            onPressed: () => _showNotifications(context),
          ),
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () => _showGlobalSearch(context),
          ),
          IconButton(
            icon: Icon(Icons.account_circle),
            onPressed: () => _showProfile(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome, ${user.profile.firstName}!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),

            _buildTodaysCasesCard(context),
            SizedBox(height: 16),

            _buildCasesOverviewCard(context),
            SizedBox(height: 16),

            _buildInternRequestsCard(context),
            SizedBox(height: 16),

            _buildQuickActionsCard(context),
            SizedBox(height: 16),

            _buildAnalyticsSnapshotCard(context),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(context, 'advocate'),
    );
  }

  Widget _buildLanguageSelector(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.language),
      onSelected: (String language) {
        AppState.selectedLanguage = language;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Language changed to ${AppState.languages[language]}')),
        );
      },
      itemBuilder: (BuildContext context) {
        return AppState.languages.entries.map((entry) {
          return PopupMenuItem<String>(
            value: entry.key,
            child: Text(entry.value),
          );
        }).toList();
      },
    );
  }

  Widget _buildTodaysCasesCard(BuildContext context) {
    final todaysHearings = AppState.dummyCases
        .expand((case) => case.hearings)
        .where((hearing) =>
            hearing.date.day == DateTime.now().day &&
            hearing.date.month == DateTime.now().month)
        .toList();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.today, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Cases to Attend Today',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (todaysHearings.isEmpty)
              Text('No hearings scheduled for today')
            else
              ...todaysHearings.map((hearing) => ListTile(
                title: Text('${hearing.court}'),
                subtitle: Text('${hearing.time} - ${hearing.judge}'),
                trailing: Icon(Icons.arrow_forward_ios),
                onTap: () => Navigator.pushNamed(context, '/case-details'),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildCasesOverviewCard(BuildContext context) {
    final totalCases = AppState.dummyCases.length;
    final activeCases = AppState.dummyCases.where((c) => c.status == 'Active').length;
    final pendingHearings = AppState.dummyCases.where((c) => c.status == 'Pending Hearing').length;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'My Cases Overview',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatColumn('Total Cases', totalCases.toString(), Colors.blue),
                _buildStatColumn('Active', activeCases.toString(), Colors.green),
                _buildStatColumn('Pending', pendingHearings.toString(), Colors.orange),
              ],
            ),
            SizedBox(height: 16),
            Text('Recent Active Cases:', style: TextStyle(fontWeight: FontWeight.w500)),
            ...AppState.dummyCases.take(2).map((case) => ListTile(
              title: Text('${case.caseType} - ${case.filingNumber}'),
              subtitle: Text('${case.clientName} - ${case.status}'),
              dense: true,
              onTap: () => Navigator.pushNamed(context, '/case-details'),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatColumn(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildInternRequestsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person_add, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'Pending Intern Requests',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: 16),
            ListTile(
              leading: CircleAvatar(
                child: Text('AJ'),
                backgroundColor: Colors.blue.shade100,
              ),
              title: Text('Alice Johnson'),
              subtitle: Text('National Law School - Wants to intern'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextButton(
                    onPressed: () => _handleInternRequest(context, 'reject'),
                    child: Text('Reject'),
                  ),
                  ElevatedButton(
                    onPressed: () => _handleInternRequest(context, 'accept'),
                    child: Text('Accept'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildQuickActionButton(
                  '+ Add New Case',
                  Icons.add,
                  Colors.green,
                  () => _showAddCaseDialog(context),
                ),
                _buildQuickActionButton(
                  'View All Cases',
                  Icons.folder,
                  Colors.blue,
                  () => Navigator.pushNamed(context, '/my-cases'),
                ),
                _buildQuickActionButton(
                  'Go to Diary',
                  Icons.calendar_today,
                  Colors.purple,
                  () => Navigator.pushNamed(context, '/diary'),
                ),
                _buildQuickActionButton(
                  'Yellow Pages',
                  Icons.phone_in_talk,
                  Colors.orange,
                  () => Navigator.pushNamed(context, '/yellow-pages'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String text, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20),
          SizedBox(width: 8),
          Flexible(child: Text(text, textAlign: TextAlign.center)),
        ],
      ),
    );
  }

  Widget _buildAnalyticsSnapshotCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Analytics Snapshot',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => Navigator.pushNamed(context, '/analytics'),
                  child: Text('View Details'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('Cases Closed', style: TextStyle(fontSize: 12)),
                          Text('15', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.blue)),
                          Text('This Month', style: TextStyle(fontSize: 10, color: Colors.grey)),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('Team Performance', style: TextStyle(fontSize: 12)),
                          Text('92%', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.green)),
                          Text('Avg. Score', style: TextStyle(fontSize: 10, color: Colors.grey)),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context, String userType) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      items: [
        BottomNavigationBarItem(
          icon: Icon(Icons.folder),
          label: 'My Cases',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.phone_in_talk),
          label: 'Yellow Pages',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.share),
          label: userType == 'intern' ? 'Share Research' : 'Share Case Info',
        ),
        BottomNavigationBarItem(
          icon: Icon(userType == 'intern' ? Icons.person : Icons.analytics),
          label: userType == 'intern' ? 'Profile' : 'Analytics',
        ),
      ],
      onTap: (index) {
        switch (index) {
          case 0:
            Navigator.pushNamed(context, '/my-cases');
            break;
          case 1:
            Navigator.pushNamed(context, '/yellow-pages');
            break;
          case 2:
            _showShareDialog(context);
            break;
          case 3:
            if (userType == 'intern') {
              _showProfile(context);
            } else {
              Navigator.pushNamed(context, '/analytics');
            }
            break;
        }
      },
    );
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Notifications', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.gavel, color: Colors.blue),
              title: Text('Case hearing tomorrow'),
              subtitle: Text('Case RN-2024-005 at 10:00 AM'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: Icon(Icons.person_add, color: Colors.green),
              title: Text('New intern request'),
              subtitle: Text('Alice Johnson wants to intern'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: Icon(Icons.update, color: Colors.orange),
              title: Text('Case status updated'),
              subtitle: Text('Case CN-2024-010 status changed'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showGlobalSearch(BuildContext context) {
    showSearch(
      context: context,
      delegate: GlobalSearchDelegate(),
    );
  }

  void _showProfile(BuildContext context) {
    final user = AppState.currentUser!;
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Profile', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            ListTile(
              leading: CircleAvatar(
                child: Text('${user.profile.firstName[0]}${user.profile.lastName[0]}'),
              ),
              title: Text('${user.profile.firstName} ${user.profile.lastName}'),
              subtitle: Text(user.email),
            ),
            ListTile(
              leading: Icon(Icons.phone),
              title: Text(user.profile.mobile),
            ),
            if (user.profile.officeName != null)
              ListTile(
                leading: Icon(Icons.business),
                title: Text(user.profile.officeName!),
              ),
            ListTile(
              leading: Icon(Icons.logout),
              title: Text('Logout'),
              onTap: () {
                AppState.currentUser = null;
                Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleInternRequest(BuildContext context, String action) {
    if (action == 'accept') {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Grant Access'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Grant access to which cases?'),
              SizedBox(height: 16),
              ...AppState.dummyCases.map((case) => CheckboxListTile(
                title: Text('${case.caseType} - ${case.filingNumber}'),
                subtitle: Text(case.clientName),
                value: true,
                onChanged: (value) {},
                dense: true,
              )),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Access granted to Alice Johnson')),
                );
              },
              child: Text('Confirm Access'),
            ),
          ],
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Intern request rejected')),
      );
    }
  }

  void _showAddCaseDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AddCaseScreen()),
    );
  }

  void _showShareDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Share Case Information', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Text('Select a case to share:'),
            SizedBox(height: 16),
            ...AppState.dummyCases.map((case) => ListTile(
              title: Text('${case.caseType} - ${case.filingNumber}'),
              subtitle: Text(case.clientName),
              trailing: Icon(Icons.share),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Sharing case information for ${case.filingNumber}')),
                );
              },
            )),
          ],
        ),
      ),
    );
  }
}20),
              Text(
                'Legal Case Management',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 40),
              _buildLanguageSelector(),
              SizedBox(height: 40),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Column(
                  children: [
                    ElevatedButton(
                      onPressed: () => Navigator.pushNamed(context, '/login'),
                      child: Text('Login'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size(double.infinity, 50),
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.blue.shade800,
                      ),
                    ),
                    SizedBox(height: 16),
                    OutlinedButton(
                      onPressed: () => Navigator.pushNamed(context, '/register'),
                      child: Text('Create New Account'),
                      style: OutlinedButton.styleFrom(
                        minimumSize: Size(double.infinity, 50),
                        foregroundColor: Colors.white,
                        side: BorderSide(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            'Select your preferred language',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: AppState.languages.entries.map((entry) {
              bool isSelected = AppState.selectedLanguage == entry.key;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    AppState.selectedLanguage = entry.key;
                  });
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.white),
                  ),
                  child: Text(
                    entry.value,
                    style: TextStyle(
                      color: isSelected ? Colors.blue.shade800 : Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

// Login Screen
class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Login'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_circle,
                size: 80,
                color: Colors.blue.shade800,
              ),
              SizedBox(height: 40),
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email/Phone Number',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email or phone number';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                decoration: InputDecoration(
                  labelText: 'Password',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your password';
                  }
                  return null;
                },
              ),
              SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Forgot password functionality not implemented')),
                    );
                  },
                  child: Text('Forgot Password?'),
                ),
              ),
              SizedBox(height: 20),
              ElevatedButton(
                onPressed: _login,
                child: Text('Login'),
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(double.infinity, 50),
                  backgroundColor: Colors.blue.shade800,
                  foregroundColor: Colors.white,
                ),
              ),
              SizedBox(height: 20),
              Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text('OR'),
                  ),
                  Expanded(child: Divider()),
                ],
              ),
              SizedBox(height: 20),
              OutlinedButton.icon(
                onPressed: () => _socialLogin('Google'),
                icon: Icon(Icons.login),
                label: Text('Continue with Google'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 50),
                ),
              ),
              SizedBox(height: 12),
              OutlinedButton.icon(
                onPressed: () => _socialLogin('Outlook'),
                icon: Icon(Icons.email),
                label: Text('Continue with Outlook'),
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(double.infinity, 50),
                ),
              ),
              SizedBox(height: 20),
              TextButton(
                onPressed: () => Navigator.pushNamed(context, '/register'),
                child: Text("Don't have an account? Sign Up."),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _login() {
    if (_formKey.currentState!.validate()) {
      String email = _emailController.text;
      String userType = email.contains('intern') ? 'Intern' : 'Advocate';

      AppState.currentUser = User(
        userId: 'user_123',
        email: email,
        userType: userType,
        languagePreference: AppState.selectedLanguage,
        profile: UserProfile(
          firstName: userType == 'Intern' ? 'Alice' : 'John',
          lastName: userType == 'Intern' ? 'Johnson' : 'Doe',
          mobile: '+91 **********',
          officeName: userType == 'Advocate' ? 'Legal Eagles Law Firm' : null,
          advocateLicenseNumber: userType == 'Advocate' ? 'ADV123456' : null,
          college: userType == 'Intern' ? 'National Law School' : null,
          age: userType == 'Intern' ? 22 : null,
        ),
        accessControl: AccessControl(
          canAddCases: userType == 'Advocate',
          canRemoveCases: userType == 'Advocate',
          canViewAnalytics: userType == 'Advocate',
          canShareAIResults: userType == 'Advocate',
          canGrantInternAccess: userType == 'Advocate',
        ),
      );

      if (userType == 'Advocate') {
        Navigator.pushReplacementNamed(context, '/advocate-dashboard');
      } else {
        Navigator.pushReplacementNamed(context, '/intern-dashboard');
      }
    }
  }

  void _socialLogin(String provider) {
    AppState.currentUser = User(
      userId: 'user_social_123',
      email: 'user@$provider.com',
      userType: 'Advocate',
      languagePreference: AppState.selectedLanguage,
      profile: UserProfile(
        firstName: 'John',
        lastName: 'Doe',
        mobile: '+91 **********',
        officeName: 'Legal Eagles Law Firm',
        advocateLicenseNumber: 'ADV123456',
      ),
      accessControl: AccessControl(
        canAddCases: true,
        canRemoveCases: true,
        canViewAnalytics: true,
        canShareAIResults: true,
        canGrantInternAccess: true,
      ),
    );

    Navigator.pushReplacementNamed(context, '/advocate-dashboard');
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}

// Persona Selection Screen
class PersonaSelectionScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('What type of user are you?'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildPersonaCard(
              context,
              'Sr. Advocate',
              'Full practice, manage cases & interns',
              Icons.account_balance,
              Colors.blue,
            ),
            SizedBox(height: 20),
            _buildPersonaCard(
              context,
              'Jr. Advocate',
              'Practicing lawyer, handle assigned cases',
              Icons.person,
              Colors.green,
            ),
            SizedBox(height: 20),
            _buildPersonaCard(
              context,
              'Intern',
              'Student learning and assisting advocates',
              Icons.school,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonaCard(BuildContext context, String title, String description, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProfileCreationScreen(userType: title),
            ),
          );
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                icon,
                size: 60,
                color: color,
              ),
              SizedBox(height: 16),
              Text(
                title,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              SizedBox(height: 8),
              Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Profile Creation Screen
class ProfileCreationScreen extends StatefulWidget {
  final String userType;

  ProfileCreationScreen({required this.userType});

  @override
  _ProfileCreationScreenState createState() => _ProfileCreationScreenState();
}

class _ProfileCreationScreenState extends State<ProfileCreationScreen> {
  final _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  int _currentStep = 0;

  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _officeNameController = TextEditingController();
  final _licenseController = TextEditingController();
  final _collegeController = TextEditingController();
  final _ageController = TextEditingController();

  bool _agreeToTerms = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create ${widget.userType} Profile'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                for (int i = 0; i < 3; i++)
                  Expanded(
                    child: Container(
                      height: 4,
                      margin: EdgeInsets.symmetric(horizontal: 2),
                      decoration: BoxDecoration(
                        color: i <= _currentStep ? Colors.blue : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Text('Step ${_currentStep + 1} of 3'),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildBasicInfoStep(),
                _buildProfessionalDetailsStep(),
                _buildReviewSubmitStep(),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _pageController.previousPage(
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Text('Back'),
                    ),
                  ),
                if (_currentStep > 0) SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentStep == 2 ? _submitForm : _nextStep,
                    child: Text(_currentStep == 2 ? 'Create Account' : 'Next'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade800,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Basic Information',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: _firstNameController,
                decoration: InputDecoration(
                  labelText: 'First Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _lastNameController,
                decoration: InputDecoration(
                  labelText: 'Last Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value?.isEmpty == true) return 'Required';
                  if (!value!.contains('@')) return 'Invalid email';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: InputDecoration(
                  labelText: 'Password',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.isEmpty == true ? 'Required' : null,
              ),
              SizedBox(height:


// Analytics Screen
class AnalyticsScreen extends StatefulWidget {
  @override
  _AnalyticsScreenState createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  String _selectedPeriod = 'Last Month';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Employee Analytics'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _selectedPeriod = value;
              });
            },
            itemBuilder: (context) => [
              PopupMenuItem(value: 'Last Month', child: Text('Last Month')),
              PopupMenuItem(value: 'Last Quarter', child: Text('Last Quarter')),
              PopupMenuItem(value: 'Custom', child: Text('Custom Range')),
            ],
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(_selectedPeriod),
                  Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMetricsOverview(),
            SizedBox(height: 20),
            _buildCaseLoadDistribution(),
            SizedBox(height: 20),
            _buildCaseStatusBreakdown(),
            SizedBox(height: 20),
            _buildJobSheetCompletion(),
            SizedBox(height: 20),
            _buildHearingAttendance(),
            SizedBox(height: 20),
            _buildIndividualPerformance(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsOverview() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Key Metrics', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard('Total Cases', '45', Colors.blue, Icons.folder),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard('Active Cases', '32', Colors.green, Icons.trending_up),
                ),
              ],
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard('Completed', '13', Colors.orange, Icons.check_circle),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard('Team Members', '8', Colors.purple, Icons.people),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 30),
          SizedBox(height: 8),
          Text(value, style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: color)),
          Text(title, style: TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildCaseLoadDistribution() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Case Load Distribution', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Container(
              height: 200,
              child: Column(
                children: [
                  _buildDistributionBar('Adv. Priya Sharma', 15, 20, Colors.blue),
                  _buildDistributionBar('Adv. Rajesh Kumar', 12, 20, Colors.green),
                  _buildDistributionBar('Alice Johnson (Intern)', 8, 20, Colors.orange),
                  _buildDistributionBar('Rohit Gupta (Intern)', 5, 20, Colors.purple),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionBar(String name, int cases, int maxCases, Color color) {
    double percentage = cases / maxCases;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(name, style: TextStyle(fontWeight: FontWeight.w500)),
              Text('$cases cases'),
            ],
          ),
          SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildCaseStatusBreakdown() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Case Status Breakdown', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusPie('Active', '71%', Colors.green),
                _buildStatusPie('Pending', '20%', Colors.orange),
                _buildStatusPie('Closed', '9%', Colors.grey),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusPie(String label, String percentage, Color color) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color.withOpacity(0.2),
            border: Border.all(color: color, width: 4),
          ),
          child: Center(
            child: Text(percentage, style: TextStyle(fontWeight: FontWeight.bold, color: color)),
          ),
        ),
        SizedBox(height: 8),
        Text(label),
      ],
    );
  }

  Widget _buildJobSheetCompletion() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Job Sheet Completion Rate', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('85%', style: TextStyle(fontSize: 36, fontWeight: FontWeight.bold, color: Colors.green)),
                    Text('Average Completion Rate'),
                    SizedBox(height: 8),
                    Text('On-time delivery across all interns', style: TextStyle(fontSize: 12, color: Colors.grey)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHearingAttendance() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Hearing Attendance Overview', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildAttendanceCard('Attended', '42', Colors.green),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildAttendanceCard('Missed', '3', Colors.red),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildAttendanceCard('Rescheduled', '5', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceCard(String label, String count, Color color) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(count, style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: color)),
          Text(label, style: TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildIndividualPerformance() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Individual Performance', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),
            _buildPerformanceCard('Adv. Priya Sharma', 'Cases: 15', 'Job Sheets: 8', '95%', Colors.green),
            _buildPerformanceCard('Adv. Rajesh Kumar', 'Cases: 12', 'Job Sheets: 6', '88%', Colors.blue),
            _buildPerformanceCard('Alice Johnson', 'Cases: 8', 'Job Sheets: 12', '92%', Colors.orange),
            _buildPerformanceCard('Rohit Gupta', 'Cases: 5', 'Job Sheets: 8', '78%', Colors.purple),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard(String name, String metric1, String metric2, String score, Color color) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.2),
          child: Text(name.split(' ').map((n) => n[0]).join(''), style: TextStyle(color: color)),
        ),
        title: Text(name),
        subtitle: Text('$metric1 • $metric2'),
        trailing: Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(score, style: TextStyle(color: color, fontWeight: FontWeight.bold)),
        ),
      ),
    );
  }
}

// Job Sheets Screen
class JobSheetsScreen extends StatefulWidget {
  @override
  _JobSheetsScreenState createState() => _JobSheetsScreenState();
}

class _JobSheetsScreenState extends State<JobSheetsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Job Sheets'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'All'),
            Tab(text: 'Assigned'),
            Tab(text: 'In Progress'),
            Tab(text: 'Completed'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildJobSheetsList(AppState.dummyJobSheets),
          _buildJobSheetsList(AppState.dummyJobSheets.where((js) => js.status == 'Assigned').toList()),
          _buildJobSheetsList(AppState.dummyJobSheets.where((js) => js.status == 'In Progress').toList()),
          _buildJobSheetsList(AppState.dummyJobSheets.where((js) => js.status == 'Completed').toList()),
        ],
      ),
    );
  }

  Widget _buildJobSheetsList(List<JobSheet> jobSheets) {
    if (jobSheets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No job sheets found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: jobSheets.length,
      itemBuilder: (context, index) {
        final jobSheet = jobSheets[index];
        return Card(
          margin: EdgeInsets.only(bottom: 12),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => JobSheetDetailsScreen(jobSheet: jobSheet),
                ),
              );
            },
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          jobSheet.title,
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                      _buildStatusChip(jobSheet.status),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    jobSheet.description,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: Colors.grey),
                      SizedBox(width: 4),
                      Text('Assigned by: ${jobSheet.assignedBy}', style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.schedule, size: 16, color: Colors.grey),
                      SizedBox(width: 4),
                      Text(
                        'Due: ${DateFormat('MMM dd, yyyy').format(jobSheet.dueDate)}',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Spacer(),
                      if (jobSheet.dueDate.difference(DateTime.now()).inDays <= 2)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Due Soon',
                            style: TextStyle(fontSize: 10, color: Colors.red),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'Assigned':
        color = Colors.blue;
        break;
      case 'In Progress':
        color = Colors.orange;
        break;
      case 'Completed':
        color = Colors.green;
        break;
      case 'Under Review':
        color = Colors.purple;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

// Job Sheet Details Screen
class JobSheetDetailsScreen extends StatefulWidget {
  final JobSheet jobSheet;

  JobSheetDetailsScreen({required this.jobSheet});

  @override
  _JobSheetDetailsScreenState createState() => _JobSheetDetailsScreenState();
}

class _JobSheetDetailsScreenState extends State<JobSheetDetailsScreen> {
  final _commentController = TextEditingController();
  String _currentStatus = '';
  List<String> _comments = [
    'Started working on this task. Will have initial research ready by tomorrow.',
    'Need clarification on the specific sections to focus on.',
  ];

  @override
  void initState() {
    super.initState();
    _currentStatus = widget.jobSheet.status;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Job Sheet Details'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.jobSheet.title,
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    _buildStatusChip(_currentStatus),
                    SizedBox(height: 16),
                    Text('Description:', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(height: 4),
                    Text(widget.jobSheet.description),
                    SizedBox(height: 16),
                    _buildDetailRow('Assigned By', widget.jobSheet.assignedBy),
                    _buildDetailRow('Assigned Date', DateFormat('MMM dd, yyyy').format(widget.jobSheet.assignedDate)),
                    _buildDetailRow('Due Date', DateFormat('MMM dd, yyyy').format(widget.jobSheet.dueDate)),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Status Update Card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Update Status', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _currentStatus,
                      decoration: InputDecoration(
                        labelText: 'Current Status',
                        border: OutlineInputBorder(),
                      ),
                      items: ['Assigned', 'In Progress', 'Under Review', 'Completed']
                          .map((status) => DropdownMenuItem(value: status, child: Text(status)))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _currentStatus = value!;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Status updated to $value')),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Deliverables Card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Deliverables', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        ElevatedButton.icon(
                          onPressed: () => _showUploadDeliverableDialog(),
                          icon: Icon(Icons.upload_file),
                          label: Text('Upload'),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    ListTile(
                      leading: Icon(Icons.description),
                      title: Text('Research Report.docx'),
                      subtitle: Text('Uploaded 2 days ago'),
                      trailing: IconButton(
                        icon: Icon(Icons.visibility),
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Opening Research Report.docx')),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Feedback Card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Feedback from Advocate', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Text('Good work on the initial research. Please focus more on recent precedents from the last 5 years and include proper citations.'),
                    ),
                    SizedBox(height: 8),
                    Text('Feedback by: ${widget.jobSheet.assignedBy}', style: TextStyle(fontSize: 12, color: Colors.grey)),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Comments Card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Comments', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 16),
                    Container(
                      height: 200,
                      child: ListView.builder(
                        itemCount: _comments.length,
                        itemBuilder: (context, index) {
                          return Container(
                            margin: EdgeInsets.only(bottom: 8),
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(_comments[index]),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _commentController,
                            decoration: InputDecoration(
                              hintText: 'Add a comment...',
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 2,
                          ),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            if (_commentController.text.isNotEmpty) {
                              setState(() {
                                _comments.add(_commentController.text);
                                _commentController.clear();
                              });
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Comment added')),
                              );
                            }
                          },
                          child: Text('Send'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'Assigned':
        color = Colors.blue;
        break;
      case 'In Progress':
        color = Colors.orange;
        break;
      case 'Completed':
        color = Colors.green;
        break;
      case 'Under Review':
        color = Colors.purple;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  void _showUploadDeliverableDialog() {
    final nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upload Deliverable'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.cloud_upload, size: 30),
                  Text('Choose file to upload'),
                ],
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Deliverable Name',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Deliverable uploaded successfully')),
              );
            },
            child: Text('Upload'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
}

// Help Screen
class HelpScreen extends StatefulWidget {
  @override
  _HelpScreenState createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _chatController = TextEditingController();
  List<Map<String, dynamic>> _chatMessages = [
    {'isBot': true, 'message': 'Hello! How can I help you today?', 'time': DateTime.now().subtract(Duration(minutes: 1))},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Help & Support'),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'AI Chat'),
            Tab(text: 'FAQ'),
            Tab(text: 'Contact'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAIChatTab(),
          _buildFAQTab(),
          _buildContactTab(),
        ],
      ),
    );
  }

  Widget _buildAIChatTab() {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: _chatMessages.length,
            itemBuilder: (context, index) {
              final message = _chatMessages[index];
              return _buildChatMessage(
                message['message'],
                message['isBot'],
                message['time'],
              );
            },
          ),
        ),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 5,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _chatController,
                  decoration: InputDecoration(
                    hintText: 'Type your question...',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                ),
              ),
              SizedBox(width: 8),
              ElevatedButton(
                onPressed: _sendMessage,
                child: Icon(Icons.send),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade800,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.all(12),
                ),
              ),
            ],
          ),
        ),